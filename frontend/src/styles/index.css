@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* DOAXVV-inspired Color Palette (Dark Theme Default) */
  --color-background: 220 26% 8%;
  --color-foreground: 210 40% 98%;
  --color-card: 220 26% 10%;
  --color-card-foreground: 210 40% 98%;
  --color-popover: 220 26% 10%;
  --color-popover-foreground: 210 40% 98%;
  --color-primary: 210 40% 98%;
  --color-primary-foreground: 220 26% 8%;
  --color-secondary: 217.2 32.6% 17.5%;
  --color-secondary-foreground: 210 40% 98%;
  --color-muted: 217.2 32.6% 17.5%;
  --color-muted-foreground: 215 20.2% 70.1%;
  --color-accent: 217.2 32.6% 17.5%;
  --color-accent-foreground: 210 40% 98%;
  --color-destructive: 0 62.8% 50.6%;
  --color-destructive-foreground: 210 40% 98%;
  --color-border: 217.2 32.6% 15.5%;
  --color-input: 217.2 32.6% 15.5%;
  --color-ring: 212.7 26.8% 83.9%;

  /* DOAXVV-inspired Accent Colors */
  --color-accent-ocean: 188 94% 55%;
  --color-accent-pink: 340 82% 65%;
  --color-accent-purple: 260 85% 70%;
  --color-accent-gold: 45 93% 65%;
  --color-accent-cyan: 180 94% 60%;
  --color-accent-coral: 15 89% 70%;

  /* Border radius */
  --radius-lg: 0.75rem;
  --radius-md: calc(0.75rem - 2px);
  --radius-sm: calc(0.75rem - 4px);
  --radius: 0.75rem;

  /* Font families */
  --font-sans: "Inter", system-ui, sans-serif;
  --font-mono: "JetBrains Mono", "Consolas", monospace;

  /* Enhanced Layout Variables */
  --header-height: 64px;
  --header-height-mobile: 56px;
  --content-padding: 24px;
  --container-max-width: 1200px;
  --container-padding: 1rem;

  /* Responsive Layout Variables */
  --responsive-padding-mobile: 1rem;
  --responsive-padding-tablet: 1.5rem;
  --responsive-padding-desktop: 2rem;
  --responsive-gap-mobile: 1rem;
  --responsive-gap-tablet: 1.5rem;
  --responsive-gap-desktop: 2rem;
  --responsive-border-radius-mobile: 0.75rem;
  --responsive-border-radius-desktop: 1rem;

  /* Enhanced Accessibility Variables */
  --min-touch-target: 44px;
  --min-icon-size: 24px;
  --min-font-size: 16px;
  --optimal-line-height: 1.6;
  --focus-ring-width: 2px;
  --focus-ring-offset: 2px;

  /* Enhanced Animation Variables */
  --theme-transition-duration: 0.2s;
  --theme-transition-easing: cubic-bezier(0.4, 0, 0.2, 1);
  --hover-transition-duration: 0.15s;
  --focus-transition-duration: 0.1s;
  --dropdown-transition-duration: 0.2s;
  --mobile-menu-transition-duration: 0.3s;
  --micro-interaction-duration: 0.1s;

  /* Enhanced Spacing System */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 0.75rem;
  --spacing-lg: 1rem;
  --spacing-xl: 1.5rem;
  --spacing-2xl: 2rem;
  --spacing-3xl: 3rem;

  /* Enhanced Typography Scale */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;

  /* Enhanced Shadow System */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Enhanced Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;

  /* Animation keyframes */
  --animate-fade-in: fadeIn 0.5s ease-in-out;
  --animate-slide-up: slideUp 0.3s ease-out;
  --animate-slide-down: slideDown 0.3s ease-out;
  --animate-shimmer: shimmer 2s infinite;
  --animate-tooltip-fade-in: tooltipFadeIn 0.15s ease-out;
  --animate-search-results-slide: searchResultsSlide 0.3s ease-out;
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(10px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  0% { transform: translateY(-10px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes tooltipFadeIn {
  0% { 
    opacity: 0; 
    transform: scale(0.95) translateY(-5px); 
  }
  100% { 
    opacity: 1; 
    transform: scale(1) translateY(0); 
  }
}

@keyframes searchResultsSlide {
  0% {
    opacity: 0;
    transform: translateY(-8px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Light theme overrides - Optimized for accessibility and visual hierarchy */
.light {
  /* Core backgrounds - Enhanced contrast and depth */
  --color-background: 210 20% 98%;
  --color-foreground: 220 15% 8%;
  --color-card: 0 0% 100%;
  --color-card-foreground: 220 15% 8%;
  --color-popover: 0 0% 100%;
  --color-popover-foreground: 220 15% 8%;

  /* Primary colors - Improved contrast for better readability */
  --color-primary: 220 15% 8%;
  --color-primary-foreground: 210 20% 98%;

  /* Secondary colors - Better visual hierarchy */
  --color-secondary: 210 15% 92%;
  --color-secondary-foreground: 220 15% 12%;

  /* Muted colors - Enhanced contrast for accessibility */
  --color-muted: 210 15% 94%;
  --color-muted-foreground: 215 15% 25%;

  /* Accent colors - Improved interaction feedback */
  --color-accent: 210 15% 90%;
  --color-accent-foreground: 220 15% 12%;

  /* Destructive colors - Better visibility and contrast */
  --color-destructive: 0 75% 42%;
  --color-destructive-foreground: 210 20% 98%;

  /* Border and input colors - Enhanced visibility */
  --color-border: 214 20% 70%;
  --color-input: 214 15% 88%;

  /* Focus ring - Improved accessibility */
  --color-ring: 220 15% 8%;
}

@layer base {
  /* Optimized global transitions */
  * {
    @apply border-border;
    transition:
      background-color var(--theme-transition-duration) var(--theme-transition-easing),
      border-color var(--theme-transition-duration) var(--theme-transition-easing),
      color var(--theme-transition-duration) var(--theme-transition-easing),
      box-shadow var(--theme-transition-duration) var(--theme-transition-easing);
  }

  /* Optimize transitions for better performance */
  .theme-sync {
    will-change: background-color, border-color, color, box-shadow;
  }

  html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
    line-height: var(--optimal-line-height);
    font-size: var(--min-font-size);
    min-height: 100vh;
    transform: translateZ(0);
    contain: layout style paint;
    overscroll-behavior: contain;
    position: relative;
    z-index: 0;
  }

  /* Consolidated body styling */
  body,
  .light body,
  .dark body {
    @apply bg-background text-foreground;
  }

  *:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  @media (prefers-reduced-motion: reduce) {
    html {
      scroll-behavior: auto;
    }
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    /* Disable hover effects for reduced motion */
    .light *:hover {
      transform: none !important;
      scale: none !important;
    }
  }

  /* Screen reader only content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Skip links for keyboard navigation */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: hsl(var(--color-primary));
    color: hsl(var(--color-primary-foreground));
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s;
  }

  .skip-link:focus {
    top: 6px;
  }

  .light .skip-link {
    background: hsl(var(--color-primary));
    color: hsl(var(--color-primary-foreground));
    box-shadow: 0 2px 4px rgb(0 0 0 / 0.2);
  }
}

@layer utilities {
  /* Enhanced Modern Card System - Optimized for performance */
  .modern-card {
    background-color: hsl(var(--color-card) / 0.95);
    backdrop-filter: blur(8px);
    border: 1px solid hsl(var(--color-border) / 0.3);
    border-radius: var(--radius-lg);
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
    transition: box-shadow 0.15s ease-out, border-color 0.15s ease-out;
    position: relative;
    will-change: transform;
  }

  .modern-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    border-color: hsl(var(--color-border) / 0.5);
  }

  .modern-card:hover {
    background-color: hsl(var(--color-card) / 0.98);
    border-color: hsl(var(--color-border) / 0.5);
    box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.12), 0 4px 6px -4px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  .modern-card:hover::before {
    opacity: 1;
  }

  /* Light mode specific card enhancements */
  .light .modern-card {
    background-color: hsl(var(--color-card));
    border-color: hsl(var(--color-border) / 0.4);
    box-shadow: 0 2px 4px -1px rgb(0 0 0 / 0.06), 0 1px 2px -1px rgb(0 0 0 / 0.06);
  }

  .light .modern-card:hover {
    background-color: hsl(var(--color-card));
    border-color: hsl(var(--color-border) / 0.6);
    box-shadow: 0 8px 16px -4px rgba(0, 0, 0, 0.1), 0 4px 8px -4px rgba(0, 0, 0, 0.1);
  }

  /* Legacy card support */
  .doax-card {
    @apply modern-card;
  }

  /* Enhanced Modern Glass Effect - Unified design */
  .modern-glass {
    background-color: hsl(var(--color-background) / 0.8);
    backdrop-filter: blur(16px);
    border: 1px solid hsl(var(--color-border) / 0.3);
    position: relative;
    overflow: hidden;
  }

  .modern-glass::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, hsl(var(--color-accent-cyan) / 0.3), transparent);
    opacity: 0.5;
  }

  .light .modern-glass {
    background-color: hsl(var(--color-background) / 0.95);
    border-color: hsl(var(--color-border) / 0.4);
    backdrop-filter: blur(20px);
  }

  .light .modern-glass::before {
    background: linear-gradient(90deg, transparent, hsl(var(--color-accent-cyan) / 0.2), transparent);
  }

  /* Gradient Text */
  .gradient-text {
    background: linear-gradient(135deg, hsl(var(--color-accent-pink)), hsl(var(--color-accent-cyan)), hsl(var(--color-accent-purple)));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 200% 200%;
    animation: gradientShift 3s ease-in-out infinite;
  }

  @keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  /* Modern Button Variants - Enhanced for light mode */
  .btn-modern {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200;
    @apply bg-accent/10 text-accent-pink border border-accent-pink/20;
    @apply hover:bg-accent-pink/10 hover:border-accent-pink/40 hover:scale-105;
    @apply focus:outline-none focus:ring-2 focus:ring-accent-pink/30 focus:ring-offset-1;
  }

  .light .btn-modern {
    @apply bg-accent-pink/5 border-accent-pink/30;
    @apply hover:bg-accent-pink/10 hover:border-accent-pink/50;
  }

  .btn-modern-primary {
    @apply btn-modern bg-accent-pink text-white border-accent-pink;
    @apply hover:bg-accent-pink/90 hover:border-accent-pink;
  }

  .btn-modern-ghost {
    @apply btn-modern bg-transparent border-transparent;
    @apply hover:bg-accent/10 hover:border-accent/20;
  }

  .light .btn-modern-ghost {
    @apply hover:bg-accent/15 hover:border-accent/30;
  }

  /* Enhanced button styling for light mode */
  .light button[class*="bg-primary"] {
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  }

  .light button[class*="bg-primary"]:hover {
    box-shadow: 0 2px 4px 0 rgb(0 0 0 / 0.1), 0 1px 2px 0 rgb(0 0 0 / 0.06);
  }

  .light button[class*="border"] {
    border-color: hsl(var(--color-border));
  }

  .light button[class*="border"]:hover {
    border-color: hsl(var(--color-border) / 0.8);
  }

  /* Enhanced form styling for light mode */
  .light input[type="text"],
  .light input[type="email"],
  .light input[type="password"],
  .light input[type="number"],
  .light input[type="search"],
  .light textarea,
  .light select {
    background-color: hsl(var(--color-input));
    border-color: hsl(var(--color-border));
    color: hsl(var(--color-foreground));
  }

  .light input[type="text"]:focus,
  .light input[type="email"]:focus,
  .light input[type="password"]:focus,
  .light input[type="number"]:focus,
  .light input[type="search"]:focus,
  .light textarea:focus,
  .light select:focus {
    border-color: hsl(var(--color-ring));
    box-shadow: 0 0 0 2px hsl(var(--color-ring) / 0.2);
  }

  .light input::placeholder,
  .light textarea::placeholder {
    color: hsl(var(--color-muted-foreground));
    opacity: 0.8;
  }

  /* Enhanced card styling for light mode */
  .light .modern-card,
  .light .doax-card,
  .light .gaming-card {
    background-color: hsl(var(--color-card));
    border-color: hsl(var(--color-border));
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  }

  .light .modern-card:hover,
  .light .doax-card:hover,
  .light .gaming-card:hover {
    background-color: hsl(var(--color-card));
    border-color: hsl(var(--color-border) / 0.85);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  /* Container enhancements for light mode */
  .light .modern-container,
  .light .modern-container-sm,
  .light .modern-container-lg {
    background-color: transparent;
  }

  /* Glass effect improvements for light mode */
  .light .glass-effect {
    background-color: hsl(var(--color-background) / 0.95);
    backdrop-filter: blur(8px);
    border-color: hsl(var(--color-border) / 0.6);
  }

  /* Enhanced navigation styling for light mode */
  .light .doax-nav-item {
    color: hsl(var(--color-muted-foreground));
  }

  .light .doax-nav-item:hover {
    background-color: hsl(var(--color-accent) / 0.15);
    color: hsl(var(--color-accent-pink));
  }

  .light .doax-nav-item:focus {
    background-color: hsl(var(--color-accent) / 0.15);
    --tw-ring-color: hsl(var(--color-accent-pink) / 0.4);
  }

  /* Mobile menu enhancements for light mode */
  .light .mobile-menu {
    background-color: hsl(var(--color-background));
    border-color: hsl(var(--color-border));
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  /* Enhanced interactive element states for light mode */
  .light .interactive-element {
    background-color: transparent;
    transition: all 0.15s ease-in-out;
  }

  .light .interactive-element:hover {
    background-color: hsl(var(--color-accent) / 0.15);
    transform: translateY(-1px);
  }

  .light .interactive-element:focus {
    background-color: hsl(var(--color-accent) / 0.2);
    outline: 2px solid hsl(var(--color-ring));
    outline-offset: 2px;
  }

  .light .interactive-element:active {
    background-color: hsl(var(--color-accent) / 0.25);
    transform: translateY(0);
  }

  /* Enhanced ripple effect for light mode */
  .light .ripple {
    background-color: hsl(var(--color-accent-pink) / 0.25);
  }

  /* Focus ring improvements for light mode */
  .light *:focus-visible {
    outline: 2px solid hsl(var(--color-ring));
    outline-offset: 2px;
    --tw-ring-color: hsl(var(--color-ring));
    --tw-ring-offset-color: hsl(var(--color-background));
  }

  /* Tooltip and popover enhancements for light mode */
  .light .tooltip,
  .light .popover {
    background-color: hsl(var(--color-popover));
    border-color: hsl(var(--color-border));
    color: hsl(var(--color-popover-foreground));
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  /* WCAG 2.1 AA Accessibility Enhancements for Light Mode */
  .light {
    /* Ensure minimum contrast ratios */
    --color-text-high-contrast: 220 15% 5%;
    --color-text-medium-contrast: 220 15% 15%;
    --color-text-low-contrast: 220 10% 35%;

    /* Enhanced focus indicators */
    --focus-ring-color: 220 15% 8%;
    --focus-ring-width: 2px;
    --focus-ring-offset: 2px;

    /* Touch target sizes (WCAG AA compliance) */
    --min-touch-target-size: 44px;
    --min-interactive-size: 24px;
  }

  /* High contrast text for critical elements */
  .light h1, .light h2, .light h3, .light h4, .light h5, .light h6 {
    color: hsl(var(--color-text-high-contrast));
    font-weight: 600;
  }

  .light p, .light span, .light div {
    color: hsl(var(--color-foreground));
  }

  /* Enhanced focus indicators for accessibility */
  .light *:focus-visible {
    outline: var(--focus-ring-width) solid hsl(var(--focus-ring-color));
    outline-offset: var(--focus-ring-offset);
    box-shadow: 0 0 0 calc(var(--focus-ring-width) + var(--focus-ring-offset)) hsl(var(--focus-ring-color) / 0.2);
  }

  /* Ensure interactive elements meet touch target requirements */
  .light button,
  .light [role="button"],
  .light input[type="button"],
  .light input[type="submit"],
  .light input[type="reset"],
  .light a {
    min-height: var(--min-touch-target-size);
    min-width: var(--min-interactive-size);
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .light {
      --color-foreground: 0 0% 0%;
      --color-background: 0 0% 100%;
      --color-border: 0 0% 20%;
      --color-muted-foreground: 0 0% 30%;
    }

    .light button,
    .light input,
    .light select,
    .light textarea {
      border: 2px solid hsl(var(--color-border));
    }

    .light *:focus-visible {
      outline: 3px solid hsl(var(--focus-ring-color));
      outline-offset: 2px;
    }
  }
}

@layer components {
  .doax-nav-item {
    @apply flex items-center gap-2 px-3 py-2 rounded-md font-medium text-muted-foreground transition-all duration-150;
    min-height: var(--min-touch-target);
  }

  .doax-nav-item:hover {
    color: hsl(var(--color-accent-pink));
    background-color: hsl(var(--color-accent) / 0.1);
  }

  .doax-nav-item:focus {
    @apply outline-none ring-2 ring-offset-2;
    --tw-ring-color: hsl(var(--color-accent-pink) / 0.3);
  }

  .gaming-card {
    @apply doax-card;
    background: linear-gradient(135deg,
      hsl(var(--color-card) / 0.8) 0%,
      hsl(var(--color-card) / 0.6) 100%);
    border-color: hsl(var(--color-accent-pink) / 0.2);
  }

  .gaming-card:hover {
    background: linear-gradient(135deg,
      hsl(var(--color-card) / 0.9) 0%,
      hsl(var(--color-card) / 0.7) 100%);
    border-color: hsl(var(--color-accent-pink) / 0.3);
    box-shadow: 0 20px 25px -5px hsl(var(--color-accent-pink) / 0.1),
                0 8px 10px -6px hsl(var(--color-accent-pink) / 0.1);
  }

  .interactive-element {
    @apply transition-colors duration-150;
    min-height: var(--min-touch-target);
  }

  .interactive-element:hover {
    background-color: hsl(var(--color-accent) / 0.1);
  }

  .interactive-element:focus {
    background-color: hsl(var(--color-accent) / 0.1);
    @apply outline-none ring-2 ring-offset-2;
    --tw-ring-color: hsl(var(--color-accent-pink) / 0.3);
  }

  .shimmer-effect {
    @apply relative overflow-hidden;
  }

  .shimmer-effect::before {
    @apply absolute inset-0;
    content: '';
    transform: translateX(-100%);
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: var(--animate-shimmer);
  }

  .shimmer-effect:hover::before {
    animation: var(--animate-shimmer);
  }

  /* Enhanced Header Navigation Styling */
  .header-nav {
    @apply sticky top-0 transition-all duration-300;
    z-index: var(--z-sticky);
    background-color: hsl(var(--color-background) / 0.85);
    backdrop-filter: blur(12px);
    border-bottom: 1px solid hsl(var(--color-border) / 0.5);
    height: var(--header-height);
    contain: layout style;
    box-shadow: var(--shadow-sm);
  }

  .light .header-nav {
    background-color: hsl(var(--color-background) / 0.95);
    border-bottom-color: hsl(var(--color-border) / 0.8);
    box-shadow: var(--shadow-md);
  }

  /* Mobile header adjustments */
  @media (max-width: 768px) {
    .header-nav {
      height: var(--header-height-mobile);
    }
  }

  /* Header logo enhancements */
  .header-logo {
    @apply flex items-center gap-2 transition-all duration-200;
    min-height: var(--min-touch-target);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
  }

  .header-logo:hover {
    transform: scale(1.02);
    background-color: hsl(var(--color-accent) / 0.05);
  }

  .header-logo:focus-visible {
    @apply outline-none ring-2 ring-accent-pink/30 ring-offset-2;
  }

  /* Header navigation items */
  .header-nav-item {
    @apply flex items-center gap-2 px-3 py-2 rounded-lg font-medium transition-all;
    min-height: var(--min-touch-target);
    transition-duration: var(--hover-transition-duration);
    position: relative;
    overflow: hidden;
  }

  .header-nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, hsl(var(--color-accent-pink) / 0.1), hsl(var(--color-accent-cyan) / 0.1));
    opacity: 0;
    transition: opacity var(--hover-transition-duration);
    border-radius: inherit;
  }

  .header-nav-item:hover::before {
    opacity: 1;
  }

  .header-nav-item:hover {
    color: hsl(var(--color-accent-pink));
    transform: translateY(-1px);
  }

  .header-nav-item:focus-visible {
    @apply outline-none ring-2 ring-accent-pink/30 ring-offset-1;
  }

  .header-nav-item.active {
    background: linear-gradient(135deg, hsl(var(--color-accent-pink) / 0.15), hsl(var(--color-accent-cyan) / 0.1));
    color: hsl(var(--color-accent-pink));
    border: 1px solid hsl(var(--color-accent-pink) / 0.3);
    box-shadow: var(--shadow-sm);
  }

  /* Modernized Compact Header Dropdown */
  .modern-dropdown-content {
    @apply rounded-lg border;
    background-color: hsl(var(--color-popover) / 0.95);
    backdrop-filter: blur(12px);
    border-color: hsl(var(--color-border) / 0.8);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    min-width: 12rem;
    max-width: 16rem;
    z-index: 50;
  }

  .light .modern-dropdown-content {
    background-color: hsl(var(--color-popover) / 0.98);
    border-color: hsl(var(--color-border));
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  }

  /* Dropdown slide-in animation */
  @keyframes dropdownSlideIn {
    0% {
      opacity: 0;
      transform: scale(0.95) translateY(-8px);
    }
    100% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  /* Modernized compact dropdown menu items */
  .modern-dropdown-item {
    @apply block w-full text-left;
    position: relative;
    overflow: hidden;
    min-height: 36px;
    padding: 0.5rem 0.75rem;
    margin: 0.125rem;
    border-radius: 0.375rem;
    transition: all 0.15s ease-out;
    font-size: 0.875rem;
    font-weight: 500;
  }

  /* Modern compact dropdown item hover and focus states */
  .modern-dropdown-item:hover {
    background: hsl(var(--color-accent) / 0.8);
    color: hsl(var(--color-accent-pink));
    transform: translateX(1px);
  }

  .modern-dropdown-item:focus {
    background: hsl(var(--color-accent) / 0.8);
    color: hsl(var(--color-accent-pink));
    outline: 2px solid hsl(var(--color-accent-pink) / 0.5);
    outline-offset: 1px;
  }

  .modern-dropdown-item:active {
    transform: scale(0.99);
  }

  .modern-dropdown-item svg {
    @apply flex-shrink-0;
    transition: transform 0.15s ease-out;
  }

  .modern-dropdown-item:hover svg {
    transform: scale(1.05);
  }

  /* Enhanced Mobile Menu Styling */
  .mobile-menu {
    background-color: hsl(var(--color-background) / 0.95);
    backdrop-filter: blur(16px);
    border-top: 1px solid hsl(var(--color-border) / 0.6);
    animation: mobileMenuSlideDown var(--mobile-menu-transition-duration) ease-out;
    box-shadow: var(--shadow-lg);
  }

  .light .mobile-menu {
    background-color: hsl(var(--color-background) / 0.98);
    border-top-color: hsl(var(--color-border));
  }

  .mobile-menu-nav {
    padding: var(--spacing-lg) 0;
    max-height: calc(100vh - var(--header-height-mobile) - 2rem);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--color-border)) transparent;
  }

  .mobile-menu-item {
    @apply flex items-center gap-3 px-4 py-3 mx-2 rounded-lg font-medium transition-all;
    min-height: var(--min-touch-target);
    transition-duration: var(--hover-transition-duration);
    position: relative;
    overflow: hidden;
  }

  .mobile-menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, hsl(var(--color-accent-pink) / 0.1), hsl(var(--color-accent-cyan) / 0.05));
    opacity: 0;
    transition: opacity var(--hover-transition-duration);
    border-radius: inherit;
  }

  .mobile-menu-item:hover::before,
  .mobile-menu-item:focus::before {
    opacity: 1;
  }

  .mobile-menu-item:hover,
  .mobile-menu-item:focus {
    color: hsl(var(--color-accent-pink));
    transform: translateX(4px);
  }

  .mobile-menu-item.active {
    background: linear-gradient(135deg, hsl(var(--color-accent-pink) / 0.15), hsl(var(--color-accent-cyan) / 0.1));
    color: hsl(var(--color-accent-pink));
    border: 1px solid hsl(var(--color-accent-pink) / 0.3);
  }

  /* Mobile menu animations */
  @keyframes mobileMenuSlideDown {
    0% {
      opacity: 0;
      transform: translateY(-10px);
      max-height: 0;
    }
    100% {
      opacity: 1;
      transform: translateY(0);
      max-height: calc(100vh - var(--header-height-mobile));
    }
  }

  /* Mobile menu hamburger animation */
  .mobile-menu-toggle {
    @apply relative w-6 h-6 transition-all duration-300;
  }

  .mobile-menu-toggle span {
    @apply absolute left-0 w-full h-0.5 bg-current transition-all duration-300;
    transform-origin: center;
  }

  .mobile-menu-toggle span:nth-child(1) {
    top: 0;
  }

  .mobile-menu-toggle span:nth-child(2) {
    top: 50%;
    transform: translateY(-50%);
  }

  .mobile-menu-toggle span:nth-child(3) {
    bottom: 0;
  }

  .mobile-menu-toggle.open span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .mobile-menu-toggle.open span:nth-child(2) {
    opacity: 0;
  }

  .mobile-menu-toggle.open span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  /* Enhanced Loading States and Micro-interactions */
  .loading-skeleton {
    background: linear-gradient(90deg, hsl(var(--color-muted)) 25%, hsl(var(--color-muted) / 0.5) 50%, hsl(var(--color-muted)) 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .light .loading-skeleton {
    background: linear-gradient(90deg, hsl(var(--color-muted) / 0.3) 25%, hsl(var(--color-muted) / 0.1) 50%, hsl(var(--color-muted) / 0.3) 75%);
  }

  /* Pulse animation for loading states */
  .pulse-loading {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  /* Bounce animation for interactive feedback */
  .bounce-in {
    animation: bounceIn 0.3s ease-out;
  }

  @keyframes bounceIn {
    0% {
      transform: scale(0.3);
      opacity: 0;
    }
    50% {
      transform: scale(1.05);
    }
    70% {
      transform: scale(0.9);
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Slide animations for mobile menu */
  .slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .slide-up {
    animation: slideUp 0.3s ease-out;
  }

  /* Fade animations */
  .fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  .fade-out {
    animation: fadeOut 0.3s ease-out;
  }

  @keyframes fadeOut {
    0% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }

  /* Scale animations for buttons */
  .scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  @keyframes scaleIn {
    0% {
      transform: scale(0.95);
      opacity: 0;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Ripple effect for touch feedback */
  .ripple-effect {
    position: relative;
    overflow: hidden;
  }

  .ripple-effect::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: hsl(var(--color-accent-pink) / 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  .ripple-effect:active::after {
    width: 300px;
    height: 300px;
  }

  /* Enhanced focus indicators */
  .focus-ring {
    position: relative;
  }

  .focus-ring::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid hsl(var(--color-accent-pink));
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.2s ease-out;
  }

  .focus-ring:focus-visible::before {
    opacity: 1;
  }

  /* Smooth transitions for theme changes */
  .theme-transition {
    transition:
      background-color 0.3s ease-out,
      border-color 0.3s ease-out,
      color 0.3s ease-out,
      box-shadow 0.3s ease-out,
      transform 0.2s ease-out;
  }

  /* Performance optimizations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }

  .contain-layout {
    contain: layout style;
  }

  /* Responsive enhancements */
  @media (max-width: 640px) {
    .header-nav {
      height: var(--header-height-mobile);
    }

    .header-logo {
      padding: var(--spacing-xs);
    }

    .mobile-menu-nav {
      padding: var(--spacing-md) 0;
    }
  }

  @media (min-width: 768px) {
    .header-nav {
      height: var(--header-height);
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .header-nav-item,
    .mobile-menu-item,
    .header-dropdown-content [role="menuitem"] {
      border: 2px solid currentColor;
    }

    .header-nav-item:focus,
    .mobile-menu-item:focus,
    .header-dropdown-content [role="menuitem"]:focus {
      outline: 3px solid hsl(var(--color-accent-pink));
      outline-offset: 2px;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .header-nav-item,
    .mobile-menu-item,
    .header-dropdown-content [role="menuitem"],
    .mobile-menu-toggle,
    .header-logo {
      transition: none !important;
      animation: none !important;
    }

    .header-nav-item:hover,
    .mobile-menu-item:hover {
      transform: none !important;
    }
  }

  /* Unified Filter Dropdown Styling - Enhanced for light mode */
  .filter-dropdown-select {
    @apply w-full border rounded-lg text-sm transition-all;
    min-height: var(--min-touch-target);
    padding: 0.5rem 0.75rem;
    background-color: hsl(var(--color-background) / 0.95);
    backdrop-filter: blur(8px);
    border-color: hsl(var(--color-border) / 0.5);
  }

  .light .filter-dropdown-select {
    background-color: hsl(var(--color-input));
    border-color: hsl(var(--color-border));
  }

  .filter-dropdown-select:focus {
    outline: none;
    border-color: hsl(var(--color-accent-pink));
    box-shadow: 0 0 0 2px hsl(var(--color-accent-pink) / 0.2);
  }

  .filter-dropdown-select:hover {
    border-color: hsl(var(--color-border) / 0.7);
  }

  .light .filter-dropdown-select:hover {
    border-color: hsl(var(--color-border) / 0.85);
    background-color: hsl(var(--color-input));
  }

  /* Black theme overrides for filter dropdowns */
  .black-theme .filter-dropdown-select {
    background-color: hsl(220 26% 8% / 0.5);
    border-color: hsl(217.2 32.6% 15.5% / 0.7);
    color: hsl(var(--color-foreground));
  }

  .black-theme .filter-dropdown-select:focus {
    border-color: hsl(217.2 32.6% 15.5% / 0.5);
  }

  .ripple {
    position: absolute;
    border-radius: 50%;
    transform: scale(0);
    animation: ripple 0.5s linear;
    background-color: rgba(240, 99, 184, 0.18);
    pointer-events: none;
    z-index: 1;
  }

  @keyframes ripple {
    to {
      transform: scale(2.5);
      opacity: 0;
    }
  }

  .header-dropdown-content [role="menuitem"]:focus-visible {
    outline: 2px solid hsl(var(--color-accent-pink));
    outline-offset: 2px;
    background: rgba(240, 99, 184, 0.12);
    color: hsl(var(--color-accent-pink));
  }

  .main-content {
    @apply flex-1 relative;
    padding-top: var(--content-padding);
    min-height: calc(100vh - var(--header-height));
    contain: layout style;
  }

  .content-wrapper {
    @apply mx-auto px-4 py-6 max-w-6xl;
  }

  /* Custom scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--color-border)) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    @apply w-2;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-border/80;
  }

  /* Enhanced Responsive Container System */
  .modern-container {
    @apply mx-auto;
    width: 100%;
    padding-left: var(--responsive-padding-mobile);
    padding-right: var(--responsive-padding-mobile);
    max-width: min(100%, 1200px);
  }

  @media (min-width: 768px) {
    .modern-container {
      padding-left: var(--responsive-padding-tablet);
      padding-right: var(--responsive-padding-tablet);
    }
  }

  @media (min-width: 1024px) {
    .modern-container {
      padding-left: var(--responsive-padding-desktop);
      padding-right: var(--responsive-padding-desktop);
    }
  }

  .modern-container-sm {
    @apply mx-auto;
    width: 100%;
    padding-left: var(--responsive-padding-mobile);
    padding-right: var(--responsive-padding-mobile);
    max-width: min(100%, 800px);
  }

  @media (min-width: 768px) {
    .modern-container-sm {
      padding-left: var(--responsive-padding-tablet);
      padding-right: var(--responsive-padding-tablet);
    }
  }

  .modern-container-lg {
    @apply mx-auto;
    width: 100%;
    padding-left: var(--responsive-padding-mobile);
    padding-right: var(--responsive-padding-mobile);
    max-width: min(100%, 1400px);
  }

  @media (min-width: 768px) {
    .modern-container-lg {
      padding-left: var(--responsive-padding-tablet);
      padding-right: var(--responsive-padding-tablet);
    }
  }

  @media (min-width: 1024px) {
    .modern-container-lg {
      padding-left: var(--responsive-padding-desktop);
      padding-right: var(--responsive-padding-desktop);
    }
  }

  /* Legacy container support with enhanced responsiveness */
  .container-responsive {
    @apply modern-container;
  }

  .compact-container {
    @apply modern-container-sm;
  }

  /* Enhanced Responsive Page Layout */
  .modern-page {
    @apply min-h-screen;
    padding-top: calc(var(--header-height-mobile) + 0.5rem);
    padding-bottom: var(--responsive-padding-mobile);
    space-y: var(--responsive-gap-mobile);
  }

  @media (min-width: 768px) {
    .modern-page {
      padding-top: calc(var(--header-height) + 0.5rem);
      padding-bottom: var(--responsive-padding-tablet);
      space-y: var(--responsive-gap-tablet);
    }
  }

  @media (min-width: 1024px) {
    .modern-page {
      padding-bottom: var(--responsive-padding-desktop);
      space-y: var(--responsive-gap-desktop);
    }
  }

  .modern-page-header {
    @apply text-center mb-6;
    padding: var(--responsive-padding-mobile);
  }

  @media (min-width: 768px) {
    .modern-page-header {
      @apply mb-8;
      padding: var(--responsive-padding-tablet);
    }
  }

  @media (min-width: 1024px) {
    .modern-page-header {
      @apply mb-12;
      padding: var(--responsive-padding-desktop);
    }
  }

  .modern-page-title {
    @apply font-bold gradient-text leading-tight;
    font-size: clamp(1.75rem, 4vw, 3rem);
  }

  @media (min-width: 768px) {
    .modern-page-title {
      font-size: clamp(2rem, 5vw, 3.5rem);
    }
  }

  .modern-page-subtitle {
    @apply text-muted-foreground max-w-2xl mx-auto leading-relaxed;
    font-size: clamp(0.9rem, 2vw, 1.125rem);
    margin-top: clamp(0.75rem, 2vw, 1rem);
  }

  /* Enhanced Responsive Grid System */
  .modern-grid {
    @apply grid;
    gap: var(--responsive-gap-mobile);
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    contain: layout style;
  }

  @media (min-width: 768px) {
    .modern-grid {
      gap: var(--responsive-gap-tablet);
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }
  }

  @media (min-width: 1024px) {
    .modern-grid {
      gap: var(--responsive-gap-desktop);
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
  }

  .modern-grid-sm {
    @apply grid;
    gap: calc(var(--responsive-gap-mobile) * 0.75);
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }

  @media (min-width: 768px) {
    .modern-grid-sm {
      gap: calc(var(--responsive-gap-tablet) * 0.75);
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
  }

  .modern-grid-lg {
    @apply grid;
    gap: calc(var(--responsive-gap-mobile) * 1.25);
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  }

  @media (min-width: 768px) {
    .modern-grid-lg {
      gap: calc(var(--responsive-gap-tablet) * 1.25);
      grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    }
  }

  @media (min-width: 1024px) {
    .modern-grid-lg {
      gap: calc(var(--responsive-gap-desktop) * 1.25);
      grid-template-columns: repeat(auto-fit, minmax(420px, 1fr));
    }
  }

  /* Legacy grid support with enhanced responsiveness */
  .performance-grid {
    @apply modern-grid;
  }

  .compact-grid {
    @apply modern-grid-sm;
  }

  /* Modern Component States - Enhanced for light mode */
  .modern-interactive {
    @apply transition-all duration-200 ease-in-out;
    @apply hover:scale-105 hover:shadow-md;
    @apply focus:outline-none focus:ring-2 focus:ring-accent-pink/30 focus:ring-offset-1;
  }

  .light .modern-interactive {
    @apply hover:shadow-lg;
    @apply focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .light .modern-interactive:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 4px 8px -2px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  .modern-loading {
    @apply animate-pulse bg-muted/50 rounded-lg;
  }

  .light .modern-loading {
    @apply bg-muted/30;
    background: linear-gradient(90deg, hsl(var(--color-muted)) 25%, hsl(var(--color-muted) / 0.5) 50%, hsl(var(--color-muted)) 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .modern-skeleton {
    @apply modern-loading h-4 w-full;
  }

  .light .modern-skeleton {
    @apply bg-muted/20;
  }

  /* New utility classes for light mode optimization */
  .light-optimized-text {
    color: hsl(var(--color-foreground));
    font-weight: 500;
    line-height: 1.6;
  }

  .light .light-optimized-text {
    color: hsl(var(--color-text-high-contrast));
    font-weight: 600;
  }

  .light-optimized-surface {
    background-color: hsl(var(--color-card));
    border: 1px solid hsl(var(--color-border));
    border-radius: var(--radius-lg);
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  }

  .light .light-optimized-surface {
    background-color: hsl(var(--color-card));
    border-color: hsl(var(--color-border));
    box-shadow: 0 2px 4px -1px rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  }

  .light-optimized-surface:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  .light .light-optimized-surface:hover {
    box-shadow: 0 8px 12px -2px rgb(0 0 0 / 0.15), 0 4px 6px -4px rgb(0 0 0 / 0.15);
  }

  /* Enhanced focus states for light mode */
  .light-focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .light-focus-ring:focus {
    --tw-ring-color: hsl(var(--color-ring));
    --tw-ring-offset-color: hsl(var(--color-background));
  }

  .light .light-focus-ring:focus-visible {
    outline: 2px solid hsl(var(--color-ring));
    outline-offset: 2px;
    box-shadow: 0 0 0 4px hsl(var(--color-ring) / 0.2);
  }

  /* Performance optimizations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }

  .contain-layout {
    contain: layout style;
  }

  /* Light mode optimization complete - Enhanced for accessibility, performance, and visual appeal */
/* CSS file has been optimized for better browser compatibility and performance */

  /* ProseMirror editor styles */
  .ProseMirror {
    @apply outline-none;
  }

  .ProseMirror h1 {
    @apply text-3xl font-bold mb-4;
  }

  .ProseMirror h2 {
    @apply text-2xl font-semibold mb-3;
  }

  .ProseMirror h3 {
    @apply text-xl font-medium mb-2;
  }

  .ProseMirror h4 {
    @apply text-lg font-medium mb-2;
  }

  .ProseMirror p {
    @apply mb-3 leading-relaxed;
  }

  .ProseMirror ul, .ProseMirror ol {
    @apply mb-3 pl-6;
  }

  .ProseMirror ol {
    @apply list-decimal;
  }

  .ProseMirror li {
    @apply mb-1;
  }

  .ProseMirror blockquote {
    @apply border-l-4 border-border pl-4 italic text-muted-foreground;
  }

  .ProseMirror pre {
    @apply bg-muted rounded-md p-4 overflow-x-auto;
  }

  .ProseMirror pre code {
    @apply bg-transparent text-sm;
  }

  .ProseMirror code {
    @apply bg-muted px-1 py-0.5 rounded text-sm font-mono;
  }

  .ProseMirror strong {
    @apply font-semibold;
  }

  .ProseMirror em {
    @apply italic;
  }

  .ProseMirror a {
    @apply text-primary underline underline-offset-2;
  }

  .ProseMirror img {
    @apply max-w-full h-auto rounded-md;
  }

  .ProseMirror table {
    @apply border-collapse border border-border;
  }

  .ProseMirror th {
    @apply border border-border px-3 py-2 bg-muted font-semibold;
  }

  .ProseMirror td {
    @apply border border-border px-3 py-2;
  }

  .ProseMirror tr:last-child td {
    @apply border-b;
  }

  .ProseMirror .highlight {
    @apply bg-yellow-200 dark:bg-yellow-900;
  }

  .ProseMirror p.is-editor-empty:first-child::before {
    @apply text-muted-foreground;
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }

  .ProseMirror:focus-within {
    @apply outline-none;
  }

  .ProseMirror .selectedCell:after {
    @apply bg-accent/20 absolute inset-0 pointer-events-none;
    content: "";
  }

  .light .ProseMirror {
    @apply text-foreground;
  }

  .light .ProseMirror p.is-editor-empty:first-child::before {
    @apply text-muted-foreground;
  }

  /* Consolidated header dropdown styling */
  .header-dropdown-portal {
    z-index: 100;
    position: absolute;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    background: hsl(var(--color-background) / 0.95);
    backdrop-filter: blur(8px);
    border: 1px solid hsl(var(--color-border) / 0.5);
    min-width: 14rem;
    width: 14rem;
  }

  .light .header-dropdown-portal {
    background: hsl(var(--color-popover));
    border-color: hsl(var(--color-border));
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  }

  /* Optimized dropdown menu item styling */
  .header-dropdown-content [role="menuitem"] {
    transition: all 0.18s ease-in-out;
  }

  .header-dropdown-content [role="menuitem"]:hover,
  .header-dropdown-content [role="menuitem"]:focus {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: scale(1.02);
  }

  .light .header-dropdown-content [role="menuitem"]:hover,
  .light .header-dropdown-content [role="menuitem"]:focus {
    background: hsl(var(--color-accent) / 0.15);
    color: hsl(var(--color-accent-pink));
    transform: scale(1.02);
  }

  .header-dropdown-content [role="menuitem"]:active {
    background: rgba(255, 255, 255, 0.15);
    transform: scale(0.98);
  }

  /* Enhanced Editor Focus Styles */
  .ProseMirror:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Smooth transitions for sticky toolbar */
  .sticky-toolbar-transition {
    transition: all 0.2s ease-in-out;
  }

  /* Focus mode styles */
  .focus-mode {
    animation: fadeIn 0.3s ease-in-out;
  }

  /* Enhanced focus states for better accessibility */
  .editor-button:focus-visible {
    outline: 2px solid hsl(var(--color-accent-cyan));
    outline-offset: 2px;
  }

  /* Fullscreen mode optimizations */
  .fullscreen-editor {
    font-size: 16px; /* Prevent zoom on mobile */
    line-height: 1.6;
  }

  /* Floating toolbar animations */
  .floating-toolbar {
    animation: slideInFromTop 0.3s ease-out;
  }

  @keyframes slideInFromTop {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Editor content area optimization */
  .editor-content {
    scroll-behavior: smooth;
  }

  /* Sticky toolbar styles */
  .toolbar-sticky {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    backdrop-filter: blur(12px);
    border-bottom: 1px solid hsl(var(--color-border));
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  /* Focus mode specific styles */
  .focus-mode .ProseMirror {
    padding: 2rem;
    max-width: 65ch;
    margin: 0 auto;
    line-height: 1.7;
    font-size: 1.1rem;
  }

  .focus-mode .ProseMirror h1,
  .focus-mode .ProseMirror h2,
  .focus-mode .ProseMirror h3 {
    margin-top: 2rem;
    margin-bottom: 1rem;
  }

  .focus-mode .ProseMirror p {
    margin-bottom: 1.25rem;
  }

  /* Enhanced button states for editors */
  .toolbar-button {
    transition: all 0.15s ease;
    position: relative;
  }

  .toolbar-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .toolbar-button.active {
    background: hsl(var(--color-accent-cyan) / 0.2);
    color: hsl(var(--color-accent-cyan));
    border-color: hsl(var(--color-accent-cyan) / 0.3);
  }

  /* Writing focus improvements */
  .writing-zone {
    position: relative;
  }

  .writing-zone::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      180deg,
      transparent 0%,
      rgba(0, 0, 0, 0.02) 10%,
      rgba(0, 0, 0, 0.02) 90%,
      transparent 100%
    );
    pointer-events: none;
    border-radius: inherit;
  }

  /* Responsive typography for editors */
  @media (min-width: 1024px) {
    .focus-mode .ProseMirror {
      font-size: 1.125rem;
      line-height: 1.75;
    }
  }

  @media (max-width: 768px) {
    .focus-mode .ProseMirror {
      padding: 1rem;
      font-size: 1rem;
    }
    
    .toolbar-sticky {
      position: relative;
    }
  }
}

/* Border utilities for enhanced loading spinners */
.border-3 {
  border-width: 3px;
}

/* Shop Section Custom Styles */
.shop-section-card {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.shop-section-card.active-owner {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(6, 182, 212, 0.2));
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 20px 25px -5px rgba(59, 130, 246, 0.2), 0 8px 10px -6px rgba(59, 130, 246, 0.2);
}

.shop-section-card.active-event {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.2), rgba(236, 72, 153, 0.2));
  border-color: rgba(168, 85, 247, 0.3);
  box-shadow: 0 20px 25px -5px rgba(168, 85, 247, 0.2), 0 8px 10px -6px rgba(168, 85, 247, 0.2);
}

.shop-section-card.active-venus {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(6, 182, 212, 0.2));
  border-color: rgba(16, 185, 129, 0.3);
  box-shadow: 0 20px 25px -5px rgba(16, 185, 129, 0.2), 0 8px 10px -6px rgba(16, 185, 129, 0.2);
}

.shop-section-card.active-vip {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(251, 146, 60, 0.2));
  border-color: rgba(245, 158, 11, 0.3);
  box-shadow: 0 20px 25px -5px rgba(245, 158, 11, 0.2), 0 8px 10px -6px rgba(245, 158, 11, 0.2);
}

.shop-section-gradient-owner {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(6, 182, 212, 0.2));
}

.shop-section-gradient-event {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.2), rgba(236, 72, 153, 0.2));
}

.shop-section-gradient-venus {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(6, 182, 212, 0.2));
}

.shop-section-gradient-vip {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(251, 146, 60, 0.2));
}

/* Enhanced Responsive Utilities */
@layer utilities {
  /* Responsive text utilities */
  .text-responsive-xs {
    font-size: clamp(0.7rem, 0.5vw + 0.6rem, 0.75rem);
    line-height: 1.4;
  }

  .text-responsive-sm {
    font-size: clamp(0.8rem, 0.6vw + 0.7rem, 0.875rem);
    line-height: 1.5;
  }

  .text-responsive-base {
    font-size: clamp(0.9rem, 0.8vw + 0.8rem, 1rem);
    line-height: 1.6;
  }

  .text-responsive-lg {
    font-size: clamp(1rem, 1vw + 0.9rem, 1.125rem);
    line-height: 1.6;
  }

  .text-responsive-xl {
    font-size: clamp(1.1rem, 1.2vw + 1rem, 1.25rem);
    line-height: 1.6;
  }

  .text-responsive-2xl {
    font-size: clamp(1.25rem, 1.5vw + 1.1rem, 1.5rem);
    line-height: 1.5;
  }

  .text-responsive-3xl {
    font-size: clamp(1.5rem, 2vw + 1.3rem, 1.875rem);
    line-height: 1.4;
  }

  /* Mobile-first utility classes */
  .mobile-only {
    display: block;
  }

  @media (min-width: 768px) {
    .mobile-only {
      display: none;
    }
  }

  .tablet-up {
    display: none;
  }

  @media (min-width: 768px) {
    .tablet-up {
      display: block;
    }
  }

  .desktop-only {
    display: none;
  }

  @media (min-width: 1024px) {
    .desktop-only {
      display: block;
    }
  }

  /* Touch-friendly utilities */
  .touch-target {
    min-height: var(--min-touch-target);
    min-width: var(--min-touch-target);
  }

  .touch-friendly {
    min-height: var(--min-touch-target);
    min-width: var(--min-touch-target);
    padding: 0.5rem;
    margin: 0.25rem;
  }

  /* Responsive flexbox utilities */
  .flex-responsive {
    display: flex;
    flex-direction: column;
    gap: var(--responsive-gap-mobile);
  }

  @media (min-width: 768px) {
    .flex-responsive {
      flex-direction: row;
      gap: var(--responsive-gap-tablet);
    }
  }

  @media (min-width: 1024px) {
    .flex-responsive {
      gap: var(--responsive-gap-desktop);
    }
  }

  /* Responsive grid utilities - Optimized */
  .grid-responsive-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
    contain: layout;
  }

  @media (min-width: 768px) {
    .grid-responsive-cards {
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.25rem;
    }
  }

  @media (min-width: 1024px) {
    .grid-responsive-cards {
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 1.5rem;
    }
  }
}
