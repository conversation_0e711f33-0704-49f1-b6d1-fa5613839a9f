-- Migration to add binary image storage fields to existing tables
-- This migration adds new columns for storing image data directly in the database
-- instead of storing file paths or URLs

USE doaxvv_handbook;

-- ============================================================================
-- 1. Add binary image storage fields to characters table
-- ============================================================================

ALTER TABLE characters 
ADD COLUMN profile_image_data LONGBLOB COMMENT 'Binary data of the character''s profile image',
ADD COLUMN profile_image_mime_type VARCHAR(50) COMMENT 'MIME type of the profile image (e.g., image/jpeg, image/png)';

-- ============================================================================
-- 2. Add binary image storage fields to swimsuits table
-- ============================================================================

ALTER TABLE swimsuits 
ADD COLUMN image_before_data LONGBLOB COMMENT 'Binary data of the before malfunction image',
ADD COLUMN image_before_mime_type VARCHAR(50) COMMENT 'MIME type of the before malfunction image',
ADD COLUMN image_after_data LONGBLOB COMMENT 'Binary data of the after malfunction image',
ADD COLUMN image_after_mime_type VARCHAR(50) COMMENT 'MIME type of the after malfunction image';

-- ============================================================================
-- 3. Add binary image storage fields to items table
-- ============================================================================

ALTER TABLE items 
ADD COLUMN icon_data LONGBLOB COMMENT 'Binary data of the item icon image',
ADD COLUMN icon_mime_type VARCHAR(50) COMMENT 'MIME type of the icon image';

-- ============================================================================
-- 4. Add binary image storage fields to bromides table
-- ============================================================================

ALTER TABLE bromides 
ADD COLUMN art_data LONGBLOB COMMENT 'Binary data of the bromide artwork image',
ADD COLUMN art_mime_type VARCHAR(50) COMMENT 'MIME type of the artwork image';

-- ============================================================================
-- 5. Add binary image storage fields to documents table
-- ============================================================================

ALTER TABLE documents 
ADD COLUMN screenshots_data JSON COMMENT 'Array of screenshot objects with binary data and metadata: [{data: base64_string, mimeType: string, filename: string}]';

-- ============================================================================
-- 6. Add binary image storage fields to update_logs table
-- ============================================================================

ALTER TABLE update_logs 
ADD COLUMN screenshots_data JSON COMMENT 'Array of screenshot objects with binary data and metadata: [{data: base64_string, mimeType: string, filename: string}]';

-- ============================================================================
-- 7. Create indexes for performance optimization
-- ============================================================================

-- Add indexes for MIME type fields to optimize queries by image type
CREATE INDEX idx_characters_image_mime ON characters(profile_image_mime_type);
CREATE INDEX idx_swimsuits_before_mime ON swimsuits(image_before_mime_type);
CREATE INDEX idx_swimsuits_after_mime ON swimsuits(image_after_mime_type);
CREATE INDEX idx_items_icon_mime ON items(icon_mime_type);
CREATE INDEX idx_bromides_art_mime ON bromides(art_mime_type);

-- ============================================================================
-- 8. Add constraints for MIME type validation
-- ============================================================================

-- Ensure only valid image MIME types are stored
ALTER TABLE characters 
ADD CONSTRAINT chk_characters_image_mime 
CHECK (profile_image_mime_type IS NULL OR profile_image_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'));

ALTER TABLE swimsuits 
ADD CONSTRAINT chk_swimsuits_before_mime 
CHECK (image_before_mime_type IS NULL OR image_before_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp')),
ADD CONSTRAINT chk_swimsuits_after_mime 
CHECK (image_after_mime_type IS NULL OR image_after_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'));

ALTER TABLE items 
ADD CONSTRAINT chk_items_icon_mime 
CHECK (icon_mime_type IS NULL OR icon_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'));

ALTER TABLE bromides 
ADD CONSTRAINT chk_bromides_art_mime 
CHECK (art_mime_type IS NULL OR art_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'));

-- ============================================================================
-- 9. Update table verification script
-- ============================================================================

-- Verify the new columns were added successfully
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'doaxvv_handbook' 
    AND COLUMN_NAME LIKE '%_data' 
    OR COLUMN_NAME LIKE '%_mime_type'
ORDER BY TABLE_NAME, ORDINAL_POSITION;
